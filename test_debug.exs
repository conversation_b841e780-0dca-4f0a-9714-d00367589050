defmodule TestDebug do
  def test_field_processing do
    # Simulate what happens in the field macro
    custom_fields = [
      {:tags, Ecto.Enum, [values: [:red, :green, :blue]]},
      {:status, :string, [default: "active"]}
    ]
    
    IO.puts("Original custom_fields:")
    IO.inspect(custom_fields)
    
    # Process like in the inference module
    processed = Enum.map(custom_fields, fn {name, type, opts} ->
      IO.puts("\nProcessing field: #{name}")
      IO.puts("Type: #{inspect(type)}")
      IO.puts("Opts: #{inspect(opts)}")
      
      # Build ecto_type
      ecto_type = build_ecto_type(type, opts)
      IO.puts("Built ecto_type: #{inspect(ecto_type)}")
      
      # Extract type and options for field generation
      {extracted_type, type_opts} = extract_type_and_options(ecto_type)
      IO.puts("Extracted type: #{inspect(extracted_type)}")
      IO.puts("Type opts: #{inspect(type_opts)}")
      
      {name, extracted_type, type_opts}
    end)
    
    IO.puts("\nProcessed fields:")
    IO.inspect(processed)
  end
  
  defp build_ecto_type(type, opts) do
    case type do
      Ecto.Enum ->
        case Keyword.get(opts, :values) do
          nil -> type
          values -> {Ecto.Enum, values: values}
        end
      _ -> type
    end
  end
  
  defp extract_type_and_options(ecto_type) do
    case ecto_type do
      {type, opts} when is_list(opts) ->
        {type, opts}
      {type, opts} when is_map(opts) ->
        {type, Map.to_list(opts)}
      type ->
        {type, []}
    end
  end
end

TestDebug.test_field_processing()
