defmodule Drops.Relation.Inference do
  alias Drops.Relation.SQL
  alias Drops.Relation.Schema.Field

  def infer_schema(relation, name, repo) do
    # Use the unified schema inference implementation
    drops_schema = SQL.Inference.infer_from_table(name, repo)

    # Get optional Ecto associations definitions AST
    association_definitions = Module.get_attribute(relation, :associations, [])

    # Generate the Ecto schema AST using the new approach
    ecto_schema_ast =
      combine_schema_with_associations_and_custom_fields(
        drops_schema,
        association_definitions,
        # No custom fields for this function
        [],
        name
      )

    {ecto_schema_ast, drops_schema}
  end

  def infer_schema_fields_only(_relation, name, repo) do
    # Use the unified schema inference implementation
    # Return only the Drops.Relation.Schema - no more Ecto AST caching
    SQL.Inference.infer_from_table(name, repo)
  end

  # Simplified function that works directly with Drops.Relation.Schema
  # and merges custom fields with cached schema fields.
  def combine_schema_with_associations_and_custom_fields(
        drops_schema,
        association_definitions,
        custom_fields,
        table_name
      ) do
    # Create Field structs for custom fields defined by the field macro
    custom_field_structs =
      Enum.map(custom_fields, fn {name, type, opts} ->
        # Extract source from opts if provided, otherwise use field name
        source = Keyword.get(opts, :source, name)
        # Use the user-provided type as the ecto_type
        # For normalized type, we'll use a simple mapping
        normalized_type = normalize_custom_field_type(type)
        Field.new(name, normalized_type, type, source)
      end)

    # Merge custom fields with cached schema fields
    # Custom fields take precedence (user-defined ecto types are respected)
    custom_field_names = Enum.map(custom_field_structs, & &1.name)

    # Filter out fields from cached schema that are redefined by custom fields
    filtered_cached_fields =
      Enum.reject(drops_schema.fields, fn field ->
        field.name in custom_field_names
      end)

    # Combine all fields
    all_fields = filtered_cached_fields ++ custom_field_structs

    # Get field names to exclude due to associations (foreign keys)
    association_field_names = extract_association_field_names(association_definitions)

    # Check if we have both timestamp fields in the merged fields
    has_timestamps =
      Enum.any?(all_fields, &(&1.name == :inserted_at)) and
        Enum.any?(all_fields, &(&1.name == :updated_at))

    # Filter out fields that should be excluded
    # Exclude association foreign key fields and timestamp fields if using timestamps() macro
    additional_exclusions =
      if has_timestamps do
        [:inserted_at, :updated_at, :id]
      else
        []
      end

    all_exclusions = association_field_names ++ additional_exclusions

    final_fields =
      Enum.reject(all_fields, fn field ->
        field.name in all_exclusions
      end)

    # Generate field definitions from the final fields
    field_definitions =
      Enum.map(final_fields, fn field ->
        if field.source != field.name do
          quote do
            Ecto.Schema.field(unquote(field.name), unquote(field.ecto_type),
              source: unquote(field.source)
            )
          end
        else
          quote do
            Ecto.Schema.field(unquote(field.name), unquote(field.ecto_type))
          end
        end
      end)

    # Add timestamps() macro if we have both timestamp fields in the merged fields
    all_field_definitions =
      if has_timestamps do
        field_definitions ++
          [
            quote do
              timestamps()
            end
          ]
      else
        field_definitions
      end

    # Create the final schema AST
    quote location: :keep do
      schema unquote(table_name) do
        (unquote_splicing(all_field_definitions))

        unquote(association_definitions)
      end
    end
  end

  # Helper function to normalize custom field types to basic types
  defp normalize_custom_field_type(type) do
    case type do
      :string -> :string
      :integer -> :integer
      :boolean -> :boolean
      :float -> :float
      :decimal -> :decimal
      :date -> :date
      :time -> :time
      :naive_datetime -> :naive_datetime
      :utc_datetime -> :utc_datetime
      :binary -> :binary
      :id -> :integer
      :binary_id -> :binary
      # For any other types, just use the type as-is
      _ -> type
    end
  end

  # Helper function to extract foreign key field names from association definitions
  # that should be excluded from inferred schema. For belongs_to associations,
  # Ecto automatically creates the foreign key field UNLESS define_field: false
  # is specified, in which case the user is responsible for defining the field.
  defp extract_association_field_names(association_definitions) do
    case association_definitions do
      # Handle single belongs_to association with options
      {:belongs_to, _meta, [field_name, _related_module, opts]} when is_list(opts) ->
        # Only exclude the foreign key field if define_field is NOT false
        # (i.e., when Ecto will create it automatically)
        if Keyword.get(opts, :define_field, true) == false do
          # User specified define_field: false, so they'll define the field themselves
          # Don't exclude it from inferred schema
          []
        else
          # Ecto will create the field automatically, so exclude it from inferred schema
          [infer_foreign_key_field_name(field_name, opts)]
        end

      {:belongs_to, _meta, [field_name, _related_module]} ->
        # No define_field option specified, defaults to true, so Ecto will create the field
        [infer_foreign_key_field_name(field_name, [])]

      # Handle block with multiple associations
      {:__block__, _meta, associations} when is_list(associations) ->
        Enum.flat_map(associations, &extract_association_field_names/1)

      # Handle other association types (has_many, many_to_many, etc.)
      {assoc_type, _meta, _args} when assoc_type in [:has_many, :has_one, :many_to_many] ->
        # These don't create foreign key fields in the current table
        []

      # Handle any other case
      _ ->
        []
    end
  end

  # Helper function to infer the foreign key field name from association name and options
  defp infer_foreign_key_field_name(association_name, opts) do
    case Keyword.get(opts, :foreign_key) do
      nil ->
        # Default foreign key naming: association_name + "_id"
        String.to_atom("#{association_name}_id")

      foreign_key when is_atom(foreign_key) ->
        foreign_key
    end
  end
end
